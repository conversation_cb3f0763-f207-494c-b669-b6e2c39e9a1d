<!-- Enhanced Background with Gradient -->
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Enhanced Breadcrumb -->
    <nav class="flex mb-8" aria-label="Breadcrumb">
      <ol role="list" class="flex items-center space-x-4 bg-white/70 backdrop-blur-sm rounded-full px-6 py-3 shadow-sm border border-white/20">
        <li>
          <%= link_to root_path, class: "text-gray-500 hover:text-indigo-600 transition-colors duration-200" do %>
            <svg class="flex-shrink-0 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
            </svg>
            <span class="sr-only">Home</span>
          <% end %>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="flex-shrink-0 h-4 w-4 text-gray-300" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
            <%= link_to "Templates", templates_path, class: "ml-3 text-sm font-medium text-gray-600 hover:text-indigo-600 transition-colors duration-200" %>
          </div>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="flex-shrink-0 h-4 w-4 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
            <span class="ml-3 text-sm font-medium text-indigo-600 truncate max-w-xs" aria-current="page"><%= @template.name %></span>
          </div>
        </li>
      </ol>
    </nav>

    <!-- Enhanced Page Header -->
    <div class="relative overflow-hidden bg-white/80 backdrop-blur-sm shadow-2xl rounded-3xl border border-white/20 mb-8">
      <div class="absolute inset-0 bg-gradient-to-r from-indigo-500/5 to-purple-500/5"></div>
      <div class="relative px-8 py-8">
        <div class="flex flex-col lg:flex-row lg:items-start lg:justify-between">
          <div class="flex-1 min-w-0">
            <div class="flex items-start mb-6">
              <div class="flex-shrink-0">
                <% case @template.template_type %>
                <% when 'newsletter' %>
                  <div class="w-16 h-16 bg-gradient-to-br from-blue-100 to-blue-200 rounded-2xl flex items-center justify-center shadow-lg border border-blue-200/50">
                    <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                      <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                    </svg>
                  </div>
                <% when 'promotional' %>
                  <div class="w-16 h-16 bg-gradient-to-br from-green-100 to-green-200 rounded-2xl flex items-center justify-center shadow-lg border border-green-200/50">
                    <svg class="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clip-rule="evenodd" />
                    </svg>
                  </div>
                <% when 'transactional' %>
                  <div class="w-16 h-16 bg-gradient-to-br from-purple-100 to-purple-200 rounded-2xl flex items-center justify-center shadow-lg border border-purple-200/50">
                    <svg class="w-8 h-8 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                    </svg>
                  </div>
                <% else %>
                  <div class="w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl flex items-center justify-center shadow-lg border border-gray-200/50">
                    <svg class="w-8 h-8 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                <% end %>
              </div>
              <div class="ml-6 flex-1">
                <h1 class="text-4xl font-bold bg-gradient-to-r from-gray-900 via-indigo-800 to-purple-800 bg-clip-text text-transparent mb-3">
                  <%= @template.name %>
                </h1>
                <div class="flex flex-col space-y-3">
                  <div class="flex items-center text-gray-600">
                    <svg class="w-5 h-5 text-indigo-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                    </svg>
                    <span class="text-lg font-medium">Subject: <%= @template.subject %></span>
                  </div>
                  <% if @template.template_type.present? %>
                    <div class="flex items-center">
                      <% category_colors = {
                        'newsletter' => 'from-blue-100 to-blue-200 text-blue-700 border-blue-200',
                        'promotional' => 'from-green-100 to-green-200 text-green-700 border-green-200',
                        'transactional' => 'from-purple-100 to-purple-200 text-purple-700 border-purple-200'
                      } %>
                      <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-gradient-to-r <%= category_colors[@template.template_type] || 'from-gray-100 to-gray-200 text-gray-700 border-gray-200' %> border shadow-sm">
                        <%= @template.template_type.humanize %> Template
                      </span>
                    </div>
                  <% end %>
                </div>
              </div>
            </div>
          
          <!-- Enhanced Action Buttons -->
          <div class="mt-6 lg:mt-0 lg:ml-4">
            <div class="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4">
              <%= link_to edit_template_path(@template), 
                  class: "group relative inline-flex items-center justify-center px-6 py-3 text-sm font-semibold rounded-2xl shadow-lg text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-300 transform hover:scale-105 hover:shadow-xl" do %>
                <div class="absolute inset-0 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-2xl blur opacity-30 group-hover:opacity-50 transition-opacity duration-300"></div>
                <svg class="relative -ml-1 mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                <span class="relative">Edit Template</span>
              <% end %>
              
              <%= link_to preview_template_path(@template), 
                  target: "_blank",
                  class: "group relative inline-flex items-center justify-center px-6 py-3 text-sm font-semibold rounded-2xl shadow-lg text-gray-700 bg-white/80 backdrop-blur-sm border border-gray-200/50 hover:bg-white hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-300 transform hover:scale-105" do %>
                <svg class="-ml-1 mr-3 h-5 w-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                Preview
              <% end %>
              
              <%= link_to duplicate_template_path(@template), 
                  method: :post,
                  class: "group relative inline-flex items-center justify-center px-6 py-3 text-sm font-semibold rounded-2xl shadow-lg text-gray-700 bg-white/80 backdrop-blur-sm border border-gray-200/50 hover:bg-white hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-300 transform hover:scale-105" do %>
                <svg class="-ml-1 mr-3 h-5 w-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
                Duplicate
              <% end %>
              
              <%= link_to template_path(@template), 
                  method: :delete,
                  data: { 
                    confirm: "Are you sure you want to delete this template? This action cannot be undone.",
                    turbo_method: :delete 
                  },
                  class: "group relative inline-flex items-center justify-center px-6 py-3 text-sm font-semibold rounded-2xl shadow-lg text-red-700 bg-white/80 backdrop-blur-sm border border-red-200/50 hover:bg-red-50 hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-300 transform hover:scale-105" do %>
                <svg class="-ml-1 mr-3 h-5 w-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
                Delete
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Enhanced Template Details -->
  <div class="mt-8 grid grid-cols-1 gap-8 lg:grid-cols-3">
    <!-- Enhanced Template Information -->
    <div class="lg:col-span-2">
      <div class="relative overflow-hidden bg-white/80 backdrop-blur-sm shadow-2xl rounded-3xl border border-white/20 mb-8">
        <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5"></div>
        <div class="relative px-8 py-6 border-b border-gray-200/50">
          <div class="flex items-center">
            <div class="w-10 h-10 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-xl flex items-center justify-center mr-4">
              <svg class="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <h2 class="text-xl font-bold bg-gradient-to-r from-gray-900 to-indigo-800 bg-clip-text text-transparent">Template Details</h2>
              <p class="mt-1 text-sm text-gray-600">Essential information about this email template</p>
            </div>
          </div>
        </div>
        <div class="relative px-8 py-8">
          <dl class="grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-2">
            <div class="group">
              <dt class="flex items-center text-sm font-semibold text-gray-700 mb-2">
                <svg class="w-4 h-4 text-indigo-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                </svg>
                Template Name
              </dt>
              <dd class="text-lg font-medium text-gray-900 bg-gray-50/50 rounded-xl px-4 py-3 border border-gray-200/50"><%= @template.name %></dd>
            </div>
            <div class="group">
              <dt class="flex items-center text-sm font-semibold text-gray-700 mb-2">
                <svg class="w-4 h-4 text-purple-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                </svg>
                Subject Line
              </dt>
              <dd class="text-lg font-medium text-gray-900 bg-gray-50/50 rounded-xl px-4 py-3 border border-gray-200/50"><%= @template.subject %></dd>
            </div>
            <div class="group">
              <dt class="flex items-center text-sm font-semibold text-gray-700 mb-2">
                <svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Created
              </dt>
              <dd class="text-lg font-medium text-gray-900 bg-gray-50/50 rounded-xl px-4 py-3 border border-gray-200/50"><%= @template.created_at.strftime("%B %d, %Y at %I:%M %p") %></dd>
            </div>
            <div class="group">
              <dt class="flex items-center text-sm font-semibold text-gray-700 mb-2">
                <svg class="w-4 h-4 text-orange-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Last Updated
              </dt>
              <dd class="text-lg font-medium text-gray-900 bg-gray-50/50 rounded-xl px-4 py-3 border border-gray-200/50"><%= @template.updated_at.strftime("%B %d, %Y at %I:%M %p") %></dd>
            </div>
          </dl>
        </div>
      </div>
      
      <!-- Enhanced Email Content Preview -->
      <div class="relative overflow-hidden bg-white/80 backdrop-blur-sm shadow-2xl rounded-3xl border border-white/20">
        <div class="absolute inset-0 bg-gradient-to-br from-green-500/5 to-blue-500/5"></div>
        <div class="relative px-8 py-6 border-b border-gray-200/50">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="w-10 h-10 bg-gradient-to-br from-green-100 to-blue-100 rounded-xl flex items-center justify-center mr-4">
                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              </div>
              <div>
                <h2 class="text-xl font-bold bg-gradient-to-r from-gray-900 to-green-800 bg-clip-text text-transparent">Email Content Preview</h2>
                <p class="mt-1 text-sm text-gray-600">See how this template appears to recipients</p>
              </div>
            </div>
            <% if @template.body.present? %>
              <div class="flex space-x-2">
                <button onclick="toggleView('preview')" id="preview-btn" class="px-4 py-2 text-sm font-semibold rounded-xl bg-gradient-to-r from-indigo-100 to-purple-100 text-indigo-700 border border-indigo-200/50 shadow-sm hover:shadow-md transition-all duration-200">
                  Preview
                </button>
                <button onclick="toggleView('source')" id="source-btn" class="px-4 py-2 text-sm font-semibold rounded-xl bg-white/80 backdrop-blur-sm text-gray-700 border border-gray-200/50 shadow-sm hover:shadow-md transition-all duration-200">
                  HTML Source
                </button>
              </div>
            <% end %>
          </div>
        </div>
        <div class="relative px-8 py-8">
          <% if @template.body.present? %>
            <div id="preview-view" class="border border-gray-200/50 rounded-2xl overflow-hidden shadow-lg">
              <iframe srcdoc="<%= html_escape(@template.body) %>" 
                      class="w-full h-96 border-0"
                      sandbox="allow-same-origin">
              </iframe>
            </div>
            
            <div id="source-view" class="hidden">
              <pre class="bg-gray-50/80 backdrop-blur-sm border border-gray-200/50 rounded-2xl p-6 text-sm overflow-x-auto shadow-inner"><code><%= @template.body %></code></pre>
            </div>
          <% else %>
            <div class="text-center py-16">
              <div class="w-20 h-20 bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <svg class="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 class="text-xl font-bold text-gray-900 mb-2">No content available</h3>
              <p class="text-gray-600 mb-8">This template doesn't have any content yet. Add some content to see the preview.</p>
              <div>
                <%= link_to "Add Content", edit_template_path(@template), class: "inline-flex items-center px-6 py-3 text-sm font-semibold rounded-2xl shadow-lg text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-300 transform hover:scale-105" do %>
                  <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Add Content
                <% end %>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>

      <!-- Enhanced Usage Statistics -->
      <div class="lg:col-span-1">
        <div class="relative overflow-hidden bg-white/80 backdrop-blur-sm shadow-2xl rounded-3xl border border-white/20 mb-8">
          <div class="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-pink-500/5"></div>
          <div class="relative px-6 py-6 border-b border-gray-200/50">
            <div class="flex items-center">
              <div class="w-10 h-10 bg-gradient-to-br from-purple-100 to-pink-100 rounded-xl flex items-center justify-center mr-4">
                <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <div>
                <h2 class="text-lg font-bold bg-gradient-to-r from-gray-900 to-purple-800 bg-clip-text text-transparent">Usage Statistics</h2>
                <p class="mt-1 text-sm text-gray-600">Performance metrics</p>
              </div>
            </div>
          </div>
          <div class="relative px-6 py-6">
            <dl class="space-y-6">
              <div class="group">
                <dt class="flex items-center text-sm font-semibold text-gray-700 mb-2">
                  <div class="w-2 h-2 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full mr-3"></div>
                  Campaigns using this template
                </dt>
                <dd class="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent ml-5"><%= @template.campaigns.count %></dd>
              </div>
              <div class="group">
                <dt class="flex items-center text-sm font-semibold text-gray-700 mb-2">
                  <div class="w-2 h-2 bg-gradient-to-r from-green-400 to-green-600 rounded-full mr-3"></div>
                  Total emails sent
                </dt>
                <dd class="text-3xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent ml-5"><%= number_with_delimiter(@template.campaigns.joins(:campaign_contacts).where.not(campaign_contacts: { sent_at: nil }).count) %></dd>
              </div>
              <div class="group">
                <dt class="flex items-center text-sm font-semibold text-gray-700 mb-2">
                  <div class="w-2 h-2 bg-gradient-to-r from-yellow-400 to-orange-600 rounded-full mr-3"></div>
                  Total emails opened
                </dt>
                <dd class="text-3xl font-bold bg-gradient-to-r from-yellow-600 to-orange-600 bg-clip-text text-transparent ml-5"><%= number_with_delimiter(@template.campaigns.joins(:campaign_contacts).where.not(campaign_contacts: { opened_at: nil }).count) %></dd>
              </div>
              <div class="group">
                <dt class="flex items-center text-sm font-semibold text-gray-700 mb-2">
                  <div class="w-2 h-2 bg-gradient-to-r from-purple-400 to-pink-600 rounded-full mr-3"></div>
                  Average open rate
                </dt>
                <dd class="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent ml-5">
                  <% total_sent = @template.campaigns.joins(:campaign_contacts).where.not(campaign_contacts: { sent_at: nil }).count %>
                  <% total_opened = @template.campaigns.joins(:campaign_contacts).where.not(campaign_contacts: { opened_at: nil }).count %>
                  <% if total_sent > 0 %>
                    <%= number_to_percentage((total_opened.to_f / total_sent) * 100, precision: 1) %>
                  <% else %>
                    0.0%
                  <% end %>
                </dd>
              </div>
            </dl>
          </div>
        </div>

        <!-- Enhanced Recent Campaigns -->
        <div class="relative overflow-hidden bg-white/80 backdrop-blur-sm shadow-2xl rounded-3xl border border-white/20">
          <div class="absolute inset-0 bg-gradient-to-br from-indigo-500/5 to-cyan-500/5"></div>
          <div class="relative px-6 py-6 border-b border-gray-200/50">
            <div class="flex items-center">
              <div class="w-10 h-10 bg-gradient-to-br from-indigo-100 to-cyan-100 rounded-xl flex items-center justify-center mr-4">
                <svg class="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
              <div>
                <h2 class="text-lg font-bold bg-gradient-to-r from-gray-900 to-indigo-800 bg-clip-text text-transparent">Recent Campaigns</h2>
                <p class="mt-1 text-sm text-gray-600">Latest campaigns using this template</p>
              </div>
            </div>
          </div>
          <div class="relative px-6 py-6">
            <% recent_campaigns = @template.campaigns.order(created_at: :desc).limit(5) %>
            <% if recent_campaigns.any? %>
              <div class="space-y-4">
                <% recent_campaigns.each do |campaign| %>
                  <div class="group relative overflow-hidden bg-white/60 backdrop-blur-sm rounded-2xl border border-gray-200/50 p-4 hover:shadow-lg hover:bg-white/80 transition-all duration-300">
                    <div class="flex items-center justify-between">
                      <div class="flex-1 min-w-0">
                        <%= link_to campaign_path(campaign), class: "text-sm font-semibold text-indigo-600 hover:text-indigo-800 truncate transition-colors duration-200" do %>
                          <%= campaign.name %>
                        <% end %>
                        <p class="text-xs text-gray-500 mt-1 flex items-center">
                          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <%= time_ago_in_words(campaign.created_at) %> ago
                        </p>
                      </div>
                      <div class="flex-shrink-0 ml-4">
                        <% status_styles = {
                          'draft' => 'from-gray-100 to-gray-200 text-gray-700 border-gray-200',
                          'scheduled' => 'from-yellow-100 to-yellow-200 text-yellow-700 border-yellow-200',
                          'sending' => 'from-blue-100 to-blue-200 text-blue-700 border-blue-200',
                          'sent' => 'from-green-100 to-green-200 text-green-700 border-green-200',
                          'failed' => 'from-red-100 to-red-200 text-red-700 border-red-200'
                        } %>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-gradient-to-r <%= status_styles[campaign.status] || status_styles['draft'] %> border shadow-sm">
                          <%= campaign.status.humanize %>
                        </span>
                      </div>
                    </div>
                  </div>
                <% end %>
              </div>
              <div class="mt-6 pt-4 border-t border-gray-200/50">
                <%= link_to  campaigns_path(template_id: @template.id), class: "inline-flex items-center text-sm font-semibold text-indigo-600 hover:text-indigo-800 transition-colors duration-200" do %>
                  <span>View all campaigns</span>
                  <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
                  </svg>
                <% end %>
              </div>
            <% else %>
              <div class="text-center py-12">
                <div class="w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                </div>
                <h3 class="text-lg font-bold text-gray-900 mb-2">No campaigns yet</h3>
                <p class="text-gray-600 mb-6">This template hasn't been used in any campaigns yet.</p>
                <div>
                  <%= link_to "Create Campaign", new_campaign_path(template_id: @template.id), class: "inline-flex items-center px-6 py-3 text-sm font-semibold rounded-2xl shadow-lg text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-300 transform hover:scale-105" do %>
                    <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Create Campaign
                  <% end %>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>
  </div>
</div>

<script>
  function toggleView(view) {
    const previewView = document.getElementById('preview-view');
    const sourceView = document.getElementById('source-view');
    const previewBtn = document.getElementById('preview-btn');
    const sourceBtn = document.getElementById('source-btn');
    
    if (view === 'preview') {
      previewView.classList.remove('hidden');
      sourceView.classList.add('hidden');
      previewBtn.classList.add('bg-gradient-to-r', 'from-indigo-100', 'to-purple-100', 'text-indigo-700', 'border-indigo-200/50');
      previewBtn.classList.remove('bg-white/80', 'backdrop-blur-sm', 'text-gray-700', 'border-gray-200/50');
      sourceBtn.classList.add('bg-white/80', 'backdrop-blur-sm', 'text-gray-700', 'border-gray-200/50');
      sourceBtn.classList.remove('bg-gradient-to-r', 'from-indigo-100', 'to-purple-100', 'text-indigo-700', 'border-indigo-200/50');
    } else {
      previewView.classList.add('hidden');
      sourceView.classList.remove('hidden');
      sourceBtn.classList.add('bg-gradient-to-r', 'from-indigo-100', 'to-purple-100', 'text-indigo-700', 'border-indigo-200/50');
      sourceBtn.classList.remove('bg-white/80', 'backdrop-blur-sm', 'text-gray-700', 'border-gray-200/50');
      previewBtn.classList.add('bg-white/80', 'backdrop-blur-sm', 'text-gray-700', 'border-gray-200/50');
      previewBtn.classList.remove('bg-gradient-to-r', 'from-indigo-100', 'to-purple-100', 'text-indigo-700', 'border-indigo-200/50');
    }
  }
  
  // Initialize with preview view
  document.addEventListener('DOMContentLoaded', function() {
    toggleView('preview');
  });
</script>