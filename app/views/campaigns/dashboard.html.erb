<% content_for :title, "Campaign Dashboard - RapidMarkt" %>

<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100"
     data-controller="campaign-dashboard campaign-dashboard-cable"
     data-campaign-dashboard-refresh-interval-value="5000"
     data-action="dashboard:update->campaign-dashboard#handleCableUpdate">
  <!-- Enhanced Header Section -->
  <div class="relative bg-white/80 backdrop-blur-lg shadow-xl border-b border-white/30">
    <div class="absolute inset-0 bg-gradient-to-r from-indigo-600/5 via-purple-600/5 to-pink-600/5"></div>
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="py-8">
        <!-- Enhanced Breadcrumb -->
        <nav class="flex mb-6" aria-label="Breadcrumb">
          <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
              <%= link_to root_path, class: "inline-flex items-center text-sm font-medium text-gray-500 hover:text-indigo-600 transition-colors duration-200" do %>
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                </svg>
                Dashboard
              <% end %>
            </li>
            <li>
              <div class="flex items-center">
                <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                </svg>
                <%= link_to campaigns_path, class: "ml-1 text-sm font-medium text-gray-500 hover:text-indigo-600 transition-colors duration-200 md:ml-2" do %>
                  Campaigns
                <% end %>
              </div>
            </li>
            <li>
              <div class="flex items-center">
                <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                </svg>
                <span class="ml-1 text-sm font-medium text-gray-700 md:ml-2">Monitoring Dashboard</span>
              </div>
            </li>
          </ol>
        </nav>
        
        <div class="md:flex md:items-center md:justify-between">
          <div class="flex-1 min-w-0">
            <div class="flex items-center space-x-3 mb-4">
              <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center shadow-lg">
                  <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
              </div>
              <div>
                <h1 class="text-3xl font-bold bg-gradient-to-r from-gray-900 via-green-800 to-emerald-800 bg-clip-text text-transparent sm:text-4xl">
                  Campaign Dashboard
                </h1>
                <p class="mt-2 text-lg text-gray-600">
                  Real-time monitoring and analytics for your email campaigns
                </p>
              </div>
            </div>
          </div>
          <div class="mt-8 lg:mt-0 lg:ml-6 flex space-x-4">
            <!-- Real-time Status Indicator -->
            <div class="flex items-center space-x-2 px-4 py-2 bg-white/80 backdrop-blur-sm rounded-xl shadow-sm border border-green-200" data-campaign-dashboard-cable-target="connectionStatus">
              <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span class="text-sm font-medium text-gray-700">Live Updates</span>
            </div>
            
            <!-- Refresh Button -->
            <button type="button" 
                    data-action="click->campaign-dashboard#refreshData"
                    class="inline-flex items-center px-4 py-2 border border-gray-200 rounded-xl shadow-sm text-sm font-medium text-gray-700 bg-white/80 backdrop-blur-sm hover:bg-gray-50 hover:border-indigo-300 hover:text-indigo-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Refresh
            </button>
            
            <%= link_to new_campaign_path, 
                class: "inline-flex items-center px-6 py-3 border border-transparent rounded-xl shadow-lg text-sm font-medium text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 hover:shadow-xl hover:-translate-y-0.5" do %>
              <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              New Campaign
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Dashboard Content -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    
    <!-- Overview Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <!-- Total Campaigns -->
      <div class="bg-white/80 backdrop-blur-sm shadow-lg rounded-2xl border border-white/50 p-6 hover:shadow-xl transition-all duration-300">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-12 h-12 bg-gradient-to-br from-blue-100 to-indigo-200 rounded-xl flex items-center justify-center">
              <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            </div>
          </div>
          <div class="ml-4 flex-1">
            <p class="text-sm font-medium text-gray-500">Total Campaigns</p>
            <p class="text-2xl font-bold text-gray-900" data-campaign-dashboard-target="totalCampaigns">
              <%= @total_campaigns || 0 %>
            </p>
          </div>
        </div>
      </div>

      <!-- Active Campaigns -->
      <div class="bg-white/80 backdrop-blur-sm shadow-lg rounded-2xl border border-white/50 p-6 hover:shadow-xl transition-all duration-300">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-12 h-12 bg-gradient-to-br from-green-100 to-emerald-200 rounded-xl flex items-center justify-center">
              <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
          </div>
          <div class="ml-4 flex-1">
            <p class="text-sm font-medium text-gray-500">Active Campaigns</p>
            <p class="text-2xl font-bold text-gray-900" data-campaign-dashboard-target="activeCampaigns">
              <%= @active_campaigns || 0 %>
            </p>
          </div>
        </div>
      </div>

      <!-- Total Recipients -->
      <div class="bg-white/80 backdrop-blur-sm shadow-lg rounded-2xl border border-white/50 p-6 hover:shadow-xl transition-all duration-300">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-12 h-12 bg-gradient-to-br from-purple-100 to-pink-200 rounded-xl flex items-center justify-center">
              <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
          </div>
          <div class="ml-4 flex-1">
            <p class="text-sm font-medium text-gray-500">Total Recipients</p>
            <p class="text-2xl font-bold text-gray-900" data-campaign-dashboard-target="totalRecipients">
              <%= @total_recipients || 0 %>
            </p>
          </div>
        </div>
      </div>

      <!-- Average Open Rate -->
      <div class="bg-white/80 backdrop-blur-sm shadow-lg rounded-2xl border border-white/50 p-6 hover:shadow-xl transition-all duration-300">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-12 h-12 bg-gradient-to-br from-orange-100 to-red-200 rounded-xl flex items-center justify-center">
              <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            </div>
          </div>
          <div class="ml-4 flex-1">
            <p class="text-sm font-medium text-gray-500">Avg. Open Rate</p>
            <p class="text-2xl font-bold text-gray-900" data-campaign-dashboard-target="averageOpenRate">
              <%= number_to_percentage(@average_open_rate || 0, precision: 1) %>
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Performance Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
      <!-- Campaign Performance Chart -->
      <div class="bg-white/80 backdrop-blur-sm shadow-lg rounded-2xl border border-white/50 overflow-hidden">
        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-100">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-lg font-semibold text-gray-900">Campaign Performance</h3>
              <p class="text-sm text-gray-600">Open and click rates over time</p>
            </div>
            <div class="flex items-center space-x-2">
              <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
              <span class="text-xs text-gray-600">Opens</span>
              <div class="w-3 h-3 bg-green-500 rounded-full"></div>
              <span class="text-xs text-gray-600">Clicks</span>
            </div>
          </div>
        </div>
        <div class="p-6">
          <canvas id="performanceChart" width="400" height="200" data-campaign-dashboard-target="performanceChart"></canvas>
        </div>
      </div>

      <!-- Campaign Status Distribution -->
      <div class="bg-white/80 backdrop-blur-sm shadow-lg rounded-2xl border border-white/50 overflow-hidden">
        <div class="bg-gradient-to-r from-purple-50 to-pink-50 px-6 py-4 border-b border-gray-100">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-lg font-semibold text-gray-900">Campaign Status</h3>
              <p class="text-sm text-gray-600">Distribution of campaign statuses</p>
            </div>
          </div>
        </div>
        <div class="p-6">
          <canvas id="statusChart" width="400" height="200" data-campaign-dashboard-target="statusChart"></canvas>
        </div>
      </div>
    </div>

    <!-- Recent Campaigns Section -->
    <div class="bg-white/80 backdrop-blur-sm shadow-lg rounded-2xl border border-white/50 overflow-hidden mb-8">
      <div class="bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 border-b border-gray-100">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-lg font-semibold text-gray-900">Recent Campaigns</h3>
            <p class="text-sm text-gray-600">Latest campaign activity and performance</p>
          </div>
          <%= link_to campaigns_path, class: "text-sm text-indigo-600 hover:text-indigo-800 font-medium transition-colors duration-200" do %>
            View All →
          <% end %>
        </div>
      </div>

      <div class="overflow-hidden">
        <% if @recent_campaigns&.any? %>
          <div class="divide-y divide-gray-100">
            <% @recent_campaigns.each do |campaign| %>
              <div class="px-6 py-4 hover:bg-gray-50/50 transition-colors duration-200" data-controller="campaign-row">
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-4 min-w-0 flex-1">
                    <!-- Campaign Status Icon -->
                    <div class="flex-shrink-0">
                      <div class="w-10 h-10 rounded-xl flex items-center justify-center
                        <%= case campaign.status
                            when 'draft' then 'bg-gray-100'
                            when 'sending' then 'bg-yellow-100'
                            when 'sent' then 'bg-green-100'
                            when 'scheduled' then 'bg-blue-100'
                            else 'bg-gray-100'
                            end %>">
                        <div class="w-3 h-3 rounded-full
                          <%= case campaign.status
                              when 'draft' then 'bg-gray-500'
                              when 'sending' then 'bg-yellow-500 animate-pulse'
                              when 'sent' then 'bg-green-500'
                              when 'scheduled' then 'bg-blue-500'
                              else 'bg-gray-500'
                              end %>"></div>
                      </div>
                    </div>

                    <!-- Campaign Info -->
                    <div class="min-w-0 flex-1">
                      <div class="flex items-center space-x-3">
                        <h4 class="text-sm font-semibold text-gray-900 truncate">
                          <%= link_to campaign.name, campaign_path(campaign), class: "hover:text-indigo-600 transition-colors duration-200" %>
                        </h4>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                          <%= case campaign.status
                              when 'draft' then 'bg-gray-100 text-gray-800'
                              when 'sending' then 'bg-yellow-100 text-yellow-800'
                              when 'sent' then 'bg-green-100 text-green-800'
                              when 'scheduled' then 'bg-blue-100 text-blue-800'
                              else 'bg-gray-100 text-gray-800'
                              end %>">
                          <%= campaign.status.humanize %>
                        </span>
                      </div>
                      <p class="text-sm text-gray-600 truncate mt-1">
                        <%= campaign.subject %>
                      </p>
                    </div>
                  </div>

                  <!-- Campaign Stats -->
                  <div class="flex items-center space-x-6">
                    <% if campaign.sent? %>
                      <div class="text-center">
                        <div class="text-sm font-semibold text-gray-900">
                          <%= campaign.campaign_contacts.where.not(sent_at: nil).count %>
                        </div>
                        <div class="text-xs text-gray-500">Sent</div>
                      </div>
                      <div class="text-center">
                        <div class="text-sm font-semibold text-green-600">
                          <%= number_to_percentage(campaign.open_rate || 0, precision: 1) %>
                        </div>
                        <div class="text-xs text-gray-500">Opens</div>
                      </div>
                      <div class="text-center">
                        <div class="text-sm font-semibold text-blue-600">
                          <%= number_to_percentage(campaign.click_rate || 0, precision: 1) %>
                        </div>
                        <div class="text-xs text-gray-500">Clicks</div>
                      </div>
                    <% else %>
                      <div class="text-center">
                        <div class="text-sm font-semibold text-gray-900">
                          <%= campaign.contacts.count %>
                        </div>
                        <div class="text-xs text-gray-500">Recipients</div>
                      </div>
                    <% end %>

                    <!-- Campaign Actions -->
                    <div class="flex items-center space-x-2">
                      <% if campaign.draft? %>
                        <%= link_to edit_campaign_path(campaign),
                            class: "inline-flex items-center px-3 py-1 border border-gray-200 rounded-lg text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200" do %>
                          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                          Edit
                        <% end %>
                        <%= link_to send_campaign_path(campaign),
                            data: { turbo_method: :post, turbo_confirm: "Are you sure you want to send this campaign?" },
                            class: "inline-flex items-center px-3 py-1 border border-transparent rounded-lg text-xs font-medium text-white bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 transition-all duration-200" do %>
                          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                          </svg>
                          Send
                        <% end %>
                      <% elsif campaign.sending? %>
                        <button type="button"
                                data-action="click->campaign-row#pauseCampaign"
                                data-campaign-id="<%= campaign.id %>"
                                class="inline-flex items-center px-3 py-1 border border-orange-200 rounded-lg text-xs font-medium text-orange-700 bg-orange-50 hover:bg-orange-100 transition-colors duration-200">
                          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          Pause
                        </button>
                        <button type="button"
                                data-action="click->campaign-row#stopCampaign"
                                data-campaign-id="<%= campaign.id %>"
                                class="inline-flex items-center px-3 py-1 border border-red-200 rounded-lg text-xs font-medium text-red-700 bg-red-50 hover:bg-red-100 transition-colors duration-200">
                          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 9l6 6m0-6l-6 6" />
                          </svg>
                          Stop
                        </button>
                      <% else %>
                        <%= link_to campaign_path(campaign),
                            class: "inline-flex items-center px-3 py-1 border border-gray-200 rounded-lg text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200" do %>
                          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                          View
                        <% end %>
                      <% end %>
                    </div>
                  </div>
                </div>
              </div>
            <% end %>
          </div>
        <% else %>
          <div class="px-6 py-12 text-center">
            <div class="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            </div>
            <h4 class="text-lg font-semibold text-gray-900 mb-2">No campaigns yet</h4>
            <p class="text-gray-600 mb-4">Create your first campaign to start monitoring performance</p>
            <%= link_to new_campaign_path,
                class: "inline-flex items-center px-4 py-2 border border-transparent rounded-xl shadow-sm text-sm font-medium text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 transition-all duration-200" do %>
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Create Campaign
            <% end %>
          </div>
        <% end %>
      </div>
    </div>

    <!-- Real-time Activity Feed -->
    <div class="bg-white/80 backdrop-blur-sm shadow-lg rounded-2xl border border-white/50 overflow-hidden">
      <div class="bg-gradient-to-r from-green-50 to-emerald-50 px-6 py-4 border-b border-gray-100">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-lg font-semibold text-gray-900">Real-time Activity</h3>
            <p class="text-sm text-gray-600">Live campaign events and interactions</p>
          </div>
          <div class="flex items-center space-x-2">
            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span class="text-xs text-gray-600">Live</span>
          </div>
        </div>
      </div>

      <div class="max-h-96 overflow-y-auto" data-campaign-dashboard-target="activityFeed">
        <% if @recent_activities&.any? %>
          <div class="divide-y divide-gray-100">
            <% @recent_activities.each do |activity| %>
              <div class="px-6 py-4 hover:bg-gray-50/50 transition-colors duration-200">
                <div class="flex items-start space-x-3">
                  <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-gradient-to-br from-green-100 to-emerald-200 rounded-lg flex items-center justify-center">
                      <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                    </div>
                  </div>
                  <div class="min-w-0 flex-1">
                    <p class="text-sm text-gray-900">
                      <span class="font-medium"><%= activity.contact.email %></span>
                      opened campaign
                      <span class="font-medium"><%= activity.campaign.name %></span>
                    </p>
                    <p class="text-xs text-gray-500 mt-1">
                      <%= time_ago_in_words(activity.opened_at || activity.sent_at) %> ago
                    </p>
                  </div>
                </div>
              </div>
            <% end %>
          </div>
        <% else %>
          <div class="px-6 py-8 text-center">
            <div class="w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center mx-auto mb-3">
              <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <p class="text-sm text-gray-600">No recent activity</p>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>

<!-- Include Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
