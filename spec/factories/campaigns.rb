FactoryBot.define do
  factory :campaign do
    association :account
    association :user
    name { "Test Campaign" }
    subject { "Test Subject Line" }
    status { "draft" }
    preview_text { "This is a preview text for the email" }
    from_name { "Test Sender" }
    from_email { "<EMAIL>" }
    content { "This is the campaign content" }
    send_type { "now" }
    media_type { "text" }
    design_theme { "modern" }

    trait :scheduled do
      status { "scheduled" }
      scheduled_at { 1.hour.from_now }
    end

    trait :sent do
      status { "sent" }
      sent_at { 1.hour.ago }
      open_rate { 25.5 }
      click_rate { 12.3 }
    end
  end
end
