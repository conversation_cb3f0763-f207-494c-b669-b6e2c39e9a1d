require 'rails_helper'

RSpec.describe Campaign, type: :model do
  describe 'associations' do
    it { should belong_to(:account) }
    it { should belong_to(:user) }
    it { should belong_to(:template).optional }
    it { should have_many(:campaign_contacts).dependent(:destroy) }
    it { should have_many(:contacts).through(:campaign_contacts) }
  end

  describe 'validations' do
    it { should validate_presence_of(:name) }
    it { should validate_presence_of(:subject) }
    it { should validate_length_of(:name).is_at_least(2).is_at_most(100) }
    it { should validate_length_of(:subject).is_at_most(255) }
    it { should validate_inclusion_of(:status).in_array(%w[draft scheduled sending sent paused cancelled]) }
  end

  describe 'attributes' do
    let(:campaign) { build(:campaign) }

    it 'can have preview_text' do
      campaign.preview_text = "This is a preview text"
      expect(campaign.preview_text).to eq("This is a preview text")
    end

    it 'allows nil preview_text' do
      campaign.preview_text = nil
      expect(campaign).to be_valid
    end
  end

  describe 'status methods' do
    let(:campaign) { build(:campaign) }

    it 'correctly identifies draft status' do
      campaign.status = 'draft'
      expect(campaign.draft?).to be true
      expect(campaign.sent?).to be false
    end

    it 'correctly identifies sent status' do
      campaign.status = 'sent'
      expect(campaign.sent?).to be true
      expect(campaign.draft?).to be false
    end
  end

  describe 'creation with all attributes' do
    it 'creates a valid campaign with preview_text' do
      account = create(:account)
      user = create(:user, account: account)

      campaign = Campaign.new(
        account: account,
        user: user,
        name: "Test Campaign",
        subject: "Test Subject",
        preview_text: "This appears in email preview",
        status: "draft",
        content: "Campaign content"
      )

      expect(campaign).to be_valid
      expect(campaign.save).to be true
      expect(campaign.preview_text).to eq("This appears in email preview")
    end
  end
end
